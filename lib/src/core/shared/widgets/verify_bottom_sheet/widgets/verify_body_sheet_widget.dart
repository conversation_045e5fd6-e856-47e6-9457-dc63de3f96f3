import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'otp_field.dart';

final _formKey = GlobalKey<FormState>();

class VerifyBodySheetWidget extends StatelessWidget {
  final String phone;
  final ValueNotifier<bool> phoneVerified;
  final Function(String? otp)? onSuccess;
  final bool shouldValidatePhone;

  const VerifyBodySheetWidget(
      {super.key,
      required this.phone,
      required this.shouldValidatePhone,
      required this.phoneVerified,
      this.onSuccess});

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Consumer(builder: (context, ref, _) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //? OTP Field
            OtpField(
              phoneVerified: phoneVerified,
              onSuccess: onSuccess,
              shouldValidatePhone: shouldValidatePhone,
            ),

            //? Timer
            // const TimerWidget(),

            //? Resend Button
            // resendButton(context: context, auth: authController, phone: phone),

            AppGaps.gap16,

            //? Submit Button
            Button(
              label: context.tr.submit,
              isWhiteText: true,
              isBold: true,
              icon: const Icon(
                Icons.arrow_forward_ios,
                color: Colors.white,
                size: 17,
              ),
              onPressed: () async {
                if (!_formKey.currentState!.validate()) return;
              },
            ),
          ],
        );
      }),
    );
  }

// Widget resendButton(
//     {required BuildContext context,
//     required AuthController auth,
//     required String phone}) {
//   return Row(
//     mainAxisAlignment: MainAxisAlignment.center,
//     children: [
//       Text(tr.didNotGetCode),
//       TextButton(
//         onPressed: auth.disabledTimer
//             ? null
//             : () async {
//                 await auth.resendCode(phone: phone);
//
//                 timerController.start();
//               },
//         child: Text(context.tr.resendCode,
//             style: const TextStyle(
//                 color: ColorManager.primaryColor,
//                 fontWeight: FontWeight.bold,
//                 fontSize: 16)),
//       ),
//     ],
//   );
// }
}
