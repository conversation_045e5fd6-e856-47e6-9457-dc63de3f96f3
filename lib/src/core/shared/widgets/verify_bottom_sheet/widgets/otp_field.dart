import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_otp_text_field/flutter_otp_text_field.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../screens/auth/controllers/verify_otp.controller.dart';
import '../../../../theme/color_manager.dart';
import '../../loading/loading.widget.dart';

class OtpField extends ConsumerWidget {
  final ValueNotifier<bool> phoneVerified;
  final Function(String? otp)? onSuccess;
  final bool shouldValidatePhone;

  const OtpField({
    super.key,
    required this.phoneVerified,
    this.onSuccess,
    required this.shouldValidatePhone,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final verifyOTPController = ref.watch(verifyOTPControllerProvider);

    void onCompleted(String verificationCode) async {
      if (shouldValidatePhone) {
        final isPhoneOTPVerified = await verifyOTPController.verifyOTP(
          otp: verificationCode,
        );

        phoneVerified.value = isPhoneOTPVerified;
      } else {
        phoneVerified.value = true;
      }

      if (!context.mounted) return;

      if (phoneVerified.value) {
        if (onSuccess != null) {
          onSuccess!(verificationCode);
        } else {
          context.back();
          showToast(context.tr.verificationSuccessful);
        }
      } else {
        showToast(context.tr.verificationCodeIsWrong, isError: true);
      }
    }

    return verifyOTPController.isLoading
        ? const LoadingWidget()
        : Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 30),
            child: OtpTextField(
              textStyle: AppTextStyles.labelLarge,
              numberOfFields: 6,
              borderColor: ColorManager.secondaryColor,
              showFieldAsBox: true,
              onCodeChanged: (String code) {},
              onSubmit: onCompleted,
            ),
          );
  }
}
