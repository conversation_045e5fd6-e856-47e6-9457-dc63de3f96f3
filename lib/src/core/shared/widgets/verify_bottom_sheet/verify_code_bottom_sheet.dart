import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/verify_body_sheet_widget.dart';

class VerifyOtpBottomSheet extends StatelessWidget {
  final String? phoneNumber;
  final ValueNotifier<bool> phoneVerified;
  final Function(String? otp)? onSuccess;

  final bool shouldValidatePhone;

  const VerifyOtpBottomSheet(
      {super.key,
      required this.phoneNumber,
      required this.phoneVerified,
      required this.shouldValidatePhone,
      this.onSuccess});

  @override
  Widget build(BuildContext context) {
    return Container(
      // padding: MediaQuery.of(context).viewInsets,
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom + 10,
        left: 20,
        right: 20,
      ),
      // margin: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: ColorManager.backgroundColor,
        borderRadius: BorderRadius.only(
            topRight: Radius.circular(AppRadius.radius12),
            topLeft: Radius.circular(AppRadius.radius12)),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            AppGaps.gap12,
            Text(
              context.tr.enterYourCode,
              style: AppTextStyles.subHeadLine,
            ),
            AppGaps.gap12,
            Text(
              context.tr.enterCodeYouReceived,
              style: AppTextStyles.labelMedium,
              textAlign: TextAlign.center,
            ),
            AppGaps.gap16,
            VerifyBodySheetWidget(
              phone: phoneNumber!,
              phoneVerified: phoneVerified,
              onSuccess: onSuccess,
              shouldValidatePhone: shouldValidatePhone,
            ),
          ],
        ),
      ),
    );
  }
}
