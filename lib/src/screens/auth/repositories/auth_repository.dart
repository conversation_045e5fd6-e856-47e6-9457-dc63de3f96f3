import 'package:barber_app/src/core/consts/network/api_endpoints.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/city_model.dart';
import '../models/provider_model.dart';

class AuthRepository with BaseRepository {
  final BaseApiServices networkApiService;

  AuthRepository({
    required this.networkApiService,
  });

  // * Login
  Future<bool> login({
    required UserModel user,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.login;

        final response = await networkApiService.postResponse(
          url,
          body: user.toLoginJson(),
          fromAuth: true,
        );

        await getCurrentUser(
          phone: filteredPhone(user.phone, withOutCountryCode: true),
        );

        return true;
      },
    );
  }

  // * Register
  Future<bool> register({
    required UserModel user,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.register;

        final response = await networkApiService.postResponse(
          url,
          body: user.toJson(),
          fromAuth: true,
        );

        response['username'] = user.name;
        response['city'] = user.city?.toJson();

        await saveUserData(response);

        return true;
      },
    );
  }

  // * Get Current User From API
  Future<UserModel> getCurrentUser({required String phone}) async {
    return baseFunction(
      () async {
        final url = '${ApiEndpoints.users}&filters[phone][\$contains]=$phone';

        final response = await networkApiService.getResponse(url);

        final data = response as List;

        final user = data.firstOrNull ?? {};

        await saveUserData(user);

        final userData = UserModel.fromJson(user);

        if (userData.documentId?.isNotEmpty ?? false) {
          await NotificationService.subscribeToTopic(
            userData.documentId!,
          );
        }

        return userData;
      },
    );
  }

  // * Save to local
  Future<void> saveUserData(Map<String, dynamic> data) async {
    final user = data.containsKey('user') ? data['user'] : data;

    final userData = UserModel.fromJson(user);

    await GetStorageService.setData(
      key: LocalKeys.user,
      value: userData.toJson(),
    );
  }

  // * Logout
  Future<void> logout() async {
    await baseFunction(
      () async {
        //! Clear Local Data
        GetStorageService.clearLocalData();
      },
    );
  }

  // * Get Cities
  Future<List<CityModel>> getCities() async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.cities;

        final response = await networkApiService.getResponse(url);

        final cities = response['data'] as List;

        final citiesList =
            cities.map((data) => CityModel.fromJson(data)).toList();

        return citiesList;
      },
    );
  }

  // * Get Providers
  Future<List<ProviderModel>> getProviders() async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.providers;

        //ApiEndpoints.providersByCity(
        //           UserModel.userCity.id,
        //         );

        final response = await networkApiService.getResponse(url);

        final providers = response['data'] as List;

        final providersList =
            providers.map((data) => ProviderModel.fromJson(data)).toList();

        return providersList;
      },
    );
  }

  // * Get Providers By City & Service
  Future<List<ProviderModel>> getProvidersByCityAndService({
    required String? serviceDocId,
  }) async {
    return baseFunction(
      () async {
        if (serviceDocId == null) return [];

        final url = ApiEndpoints.providersByCityAndService(
          serviceDocId,
        );

        final response = await networkApiService.getResponse(url);

        final providers = response['data'] as List;

        final providersList =
            providers.map((data) => ProviderModel.fromJson(data)).toList();

        return providersList;
      },
    );
  }

  // * Get Closest Provider By Lat Lng
  Future<ProviderModel?> getClosestProviderByLatLng({
    required String? lat,
    required String? lng,
    DateTime? date,
    required String serviceDocId,
  }) async {
    return baseFunction(
      () async {
        if (lat == null || lng == null) return null;

        String url = ApiEndpoints.closestProvidersByLatLng(
          lat,
          lng,
        );

        // if (date != null) {
        //   final dateString = date.formatDateToString;
        //   final timeString = date.formatTimeToStringWithoutPm;
        //   url += '&date=$dateString&time=$timeString';
        // } else {
        //   final nowDate = DateTime.now();
        //
        //   final dateString = nowDate.formatDateToString;
        //   final timeString = nowDate.formatTimeToStringWithoutPm;
        //
        //   url += '&date=$dateString&time=$timeString';
        // }

        url += '&service=$serviceDocId';

        final response = await networkApiService.getResponse(url);

        // Handle the new response structure where it's an object with day keys
        if (response is Map<String, dynamic>) {
          // Create a provider model with the availableTimesByDay structure
          final provider = ProviderModel(
            id: 0, // Placeholder since we don't have provider details in this response
            documentId: '',
            name: '',
            phone: '',
            address: '',
            bio: '',
            availableTimesByDay: response.map(
              (day, timeSlots) => MapEntry(
                day,
                timeSlots is List
                    ? timeSlots
                        .where((timeSlot) =>
                            timeSlot != null &&
                            timeSlot is Map<String, dynamic> &&
                            timeSlot.isNotEmpty)
                        .map((timeSlot) =>
                            AvailableTimeSlotModel.fromJson(timeSlot))
                        .toList()
                    : <AvailableTimeSlotModel>[],
              ),
            ),
          );

          return provider;
        }

        // Fallback for old array structure (if still used)
        if (response is List && response.isNotEmpty) {
          final provider = ProviderModel.fromJson(response.first);
          return provider;
        }

        return null;
      },
    );
  }

  // * Get Provider Available Times By Lat Lng
  Future<ProviderModel?> getProviderAvailableTimesByLatLng({
    required String? lat,
    required String? lng,
    required String serviceDocId,
    required String providerDocId,
  }) async {
    return baseFunction(
      () async {
        if (lat == null || lng == null) return null;

        final url = ApiEndpoints.providerAvailableTimesByLatLng(
          lat,
          lng,
          serviceDocId,
          providerDocId,
        );

        final response = await networkApiService.getResponse(url);

        Log.w('Provider Available Times: $url fff $response');

        // Handle the response structure where it's an object with day keys
        if (response is Map<String, dynamic>) {
          // Create a provider model with the availableTimesByDay structure
          final provider = ProviderModel(
            id: 0, // Placeholder since we don't have provider details in this response
            documentId: providerDocId,
            name: '',
            phone: '',
            address: '',
            bio: '',
            availableTimesByDay: response.map(
              (day, timeSlots) => MapEntry(
                day,
                timeSlots is List
                    ? timeSlots
                        .where((timeSlot) =>
                            timeSlot != null &&
                            timeSlot is Map<String, dynamic> &&
                            timeSlot.isNotEmpty)
                        .map((timeSlot) =>
                            AvailableTimeSlotModel.fromJson(timeSlot))
                        .toList()
                    : <AvailableTimeSlotModel>[],
              ),
            ),
          );

          return provider;
        }

        return null;
      },
    );
  }

  Future<bool> updateProfile({required UserModel user}) async {
    return baseFunction(
      () async {
        final url = '${ApiEndpoints.updateUser}/${UserModel.currentUser.id}';
        await networkApiService.putResponse(url, data: user.toUpdateJson());
        await saveUserData(user.toJson());
        return true;
      },
    );
  }

  // * Reset Password
  Future<bool> resetPassword({
    required String phone,
    required String newPassword,
  }) async {
    return baseFunction(
      () async {
        // Find user by phone number
        final url = '${ApiEndpoints.users}&filters[phone][\$contains]=$phone';
        final response = await networkApiService.getResponse(url);

        if (response['data'] == null || response['data'].isEmpty) {
          throw Exception('User not found');
        }

        final userId = response['data'][0]['id'];

        // Update password
        final updateUrl = '${ApiEndpoints.updateUser}/$userId';
        await networkApiService.putResponse(updateUrl, data: {
          'password': newPassword,
        });

        return true;
      },
    );
  }
}

// [
//           {
//             "id": 42,
//             "documentId": "us0wkq7mpa839f2gd2mipax8",
//             "name": "Test Provider",
//             "phone": "**********",
//             "address": "Palestine Street",
//             "createdAt": "2025-02-04T08:23:49.574Z",
//             "updatedAt": "2025-06-19T17:30:34.880Z",
//             "publishedAt": "2025-06-19T17:30:34.913Z",
//             "locale": null,
//             "bio":
//                 "أنا أحمد علي، حلاق محترف بخبرة تزيد عن 10 سنوات في مجال الحلاقة والعناية بالشعر واللحية. أهتم بأدق التفاصيل لضمان حصول عملائي على تجربة مميزة ومظهر أنيق يناسبهم\n\nالتخصصات:\nقص الشعر الكلاسيكي والحديث\nتشذيب اللحية وتشكيلها\nحلاقة الذقن باستخدام شفرة حادة\nالعناية بالشعر بالزيوت الطبيعية\nتلوين الشعر واللحية\nحجامة",
//             "provider_percent": 30,
//             "lat": "32.48873259382224",
//             "lng": "35.097638703882694",
//             "password": "123456789",
//             "active": true,
//             "orders": [
//               {
//                 "id": 95,
//                 "documentId": "ygr79tdf3jd79jxagwz073s5",
//                 "total": 308.26,
//                 "delivery_cost": 158.26,
//                 "date": "2025-04-16T23:30:00.000Z",
//                 "createdAt": "2025-03-28T04:48:42.131Z",
//                 "updatedAt": "2025-04-17T10:07:22.832Z",
//                 "publishedAt": "2025-04-17T10:07:22.865Z",
//                 "locale": null,
//                 "order_number": 341007,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "confirmed",
//                 "is_service": true,
//                 "lat": "24.537145258981507",
//                 "lng": "46.76978621631861",
//                 "paid_online": null,
//                 "system_paid_amount": null,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 114,
//                 "documentId": "l0nar18eihaagt4nhnge1g19",
//                 "total": 150,
//                 "delivery_cost": 0,
//                 "date": "2025-04-30T11:00:00.000Z",
//                 "createdAt": "2025-03-28T05:04:24.496Z",
//                 "updatedAt": "2025-04-30T11:50:04.879Z",
//                 "publishedAt": "2025-04-30T11:50:04.914Z",
//                 "locale": null,
//                 "order_number": 816125,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "confirmed",
//                 "is_service": true,
//                 "lat": "24.710615274853158",
//                 "lng": "46.69154483824968",
//                 "paid_online": 50,
//                 "system_paid_amount": 45,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 94,
//                 "documentId": "dnclmd6syj651h9w78ym83pv",
//                 "total": 219.01,
//                 "delivery_cost": 69.01,
//                 "date": "2025-03-29T23:30:00.000Z",
//                 "createdAt": "2025-03-28T04:48:11.175Z",
//                 "updatedAt": "2025-04-17T07:54:55.359Z",
//                 "publishedAt": "2025-04-17T07:54:55.390Z",
//                 "locale": null,
//                 "order_number": 691717,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "done",
//                 "is_service": true,
//                 "lat": "24.609463233431832",
//                 "lng": "46.76037736237049",
//                 "paid_online": null,
//                 "system_paid_amount": null,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 118,
//                 "documentId": "xyv7psmkvlmcna3ckwj9fe0u",
//                 "total": 150,
//                 "delivery_cost": 0,
//                 "date": "2025-03-29T23:30:00.000Z",
//                 "createdAt": "2025-03-28T05:04:10.495Z",
//                 "updatedAt": "2025-05-01T07:11:09.049Z",
//                 "publishedAt": "2025-05-01T07:11:09.066Z",
//                 "locale": null,
//                 "order_number": 39762,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "confirmed",
//                 "is_service": true,
//                 "lat": "24.70472647983222",
//                 "lng": "46.69564191251993",
//                 "paid_online": null,
//                 "system_paid_amount": 45,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 120,
//                 "documentId": "a6qaq1wgzgio7vbzgydr6b2o",
//                 "total": 150,
//                 "delivery_cost": 0,
//                 "date": "2025-03-29T23:00:00.000Z",
//                 "createdAt": "2025-03-28T05:03:45.156Z",
//                 "updatedAt": "2025-05-01T07:12:58.733Z",
//                 "publishedAt": "2025-05-01T07:12:58.765Z",
//                 "locale": null,
//                 "order_number": 838873,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "confirmed",
//                 "is_service": true,
//                 "lat": "24.717084282777606",
//                 "lng": "46.68413322418928",
//                 "paid_online": null,
//                 "system_paid_amount": 45,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 108,
//                 "documentId": "jkby4lb3e697ns3vjcn55eqh",
//                 "total": 150,
//                 "delivery_cost": 0,
//                 "date": "2025-03-30T10:30:00.000Z",
//                 "createdAt": "2025-03-28T04:58:35.510Z",
//                 "updatedAt": "2025-04-28T14:39:27.956Z",
//                 "publishedAt": "2025-04-28T14:39:27.986Z",
//                 "locale": null,
//                 "order_number": 175819,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "pending",
//                 "is_service": true,
//                 "lat": "24.685257117829412",
//                 "lng": "46.68351028114557",
//                 "paid_online": null,
//                 "system_paid_amount": 45,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 109,
//                 "documentId": "l2voc4gvmivnoykw089cq38e",
//                 "total": 150,
//                 "delivery_cost": 0,
//                 "date": "2025-03-29T23:30:00.000Z",
//                 "createdAt": "2025-03-28T04:58:06.672Z",
//                 "updatedAt": "2025-04-28T14:41:27.609Z",
//                 "publishedAt": "2025-04-28T14:41:27.641Z",
//                 "locale": null,
//                 "order_number": 835639,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "pending",
//                 "is_service": true,
//                 "lat": "24.693813167570074",
//                 "lng": "46.68090283870697",
//                 "paid_online": null,
//                 "system_paid_amount": 45,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 104,
//                 "documentId": "v7y0wl8hesn69id9dq8l9559",
//                 "total": 150,
//                 "delivery_cost": 0,
//                 "date": "2025-03-29T23:30:00.000Z",
//                 "createdAt": "2025-03-28T04:51:00.059Z",
//                 "updatedAt": "2025-04-26T20:05:59.942Z",
//                 "publishedAt": "2025-04-26T20:05:59.981Z",
//                 "locale": null,
//                 "order_number": 454786,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "pending",
//                 "is_service": true,
//                 "lat": "24.680266756916037",
//                 "lng": "46.68669641017914",
//                 "paid_online": null,
//                 "system_paid_amount": null,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 110,
//                 "documentId": "wlt1atoo0kcgz2lbehakq7zw",
//                 "total": 150,
//                 "delivery_cost": 0,
//                 "date": "2025-04-21T22:00:00.000Z",
//                 "createdAt": "2025-03-28T04:58:55.144Z",
//                 "updatedAt": "2025-04-28T14:41:49.400Z",
//                 "publishedAt": "2025-04-28T14:41:49.420Z",
//                 "locale": null,
//                 "order_number": 936485,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "pending",
//                 "is_service": true,
//                 "lat": "24.71210342009892",
//                 "lng": "46.67238716036081",
//                 "paid_online": null,
//                 "system_paid_amount": null,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 122,
//                 "documentId": "wplnhcjewr533nfdheq04z0z",
//                 "total": 150,
//                 "delivery_cost": 0,
//                 "date": "2025-04-30T11:00:00.000Z",
//                 "createdAt": "2025-05-22T14:10:44.412Z",
//                 "updatedAt": "2025-05-22T14:10:46.915Z",
//                 "publishedAt": "2025-05-22T14:10:46.937Z",
//                 "locale": null,
//                 "order_number": 816125,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "confirmed",
//                 "is_service": true,
//                 "lat": "24.710615274853158",
//                 "lng": "46.69154483824968",
//                 "paid_online": 50,
//                 "system_paid_amount": 45,
//                 "page_request_uid": null
//               },
//               {
//                 "id": 127,
//                 "documentId": "zahken010mo51lft8dyjofu2",
//                 "total": 308.26,
//                 "delivery_cost": 158.26,
//                 "date": "2025-05-22T06:00:00.000Z",
//                 "createdAt": "2025-05-22T14:14:42.494Z",
//                 "updatedAt": "2025-05-22T19:32:06.765Z",
//                 "publishedAt": "2025-05-22T19:32:06.783Z",
//                 "locale": null,
//                 "order_number": 341007,
//                 "discount": 0,
//                 "phone": "**********",
//                 "note": "",
//                 "address": "Palestine Street",
//                 "order_status": "confirmed",
//                 "is_service": true,
//                 "lat": "24.537145258981507",
//                 "lng": "46.76978621631861",
//                 "paid_online": null,
//                 "system_paid_amount": null,
//                 "page_request_uid": null
//               }
//             ],
//             "work_times": [
//               {
//                 "id": 96,
//                 "from": "08:00:00.000",
//                 "to": "16:15:00.000",
//                 "day": "Tuesday"
//               },
//               {
//                 "id": 97,
//                 "from": "12:40:00.000",
//                 "to": "15:40:00.000",
//                 "day": "Wednesday"
//               },
//               {
//                 "id": 98,
//                 "from": "08:00:00.000",
//                 "to": "23:45:00.000",
//                 "day": "Monday"
//               }
//             ],
//             "services": [
//               {
//                 "id": 23,
//                 "service": {
//                   "id": 30,
//                   "documentId": "a3mvwmos00hnkz362yapicui",
//                   "name": "2حجامة",
//                   "description":
//                       "✨ الحجامة ليست مجرد علاج، بل أسلوب حياة صحي! جربها لتشعر بالفرق ✨ \n\nمميزات الحجامة\n\nتنشيط الدورة الدموية: تساعد الحجامة في تحسين تدفق الدم إلى الأنسجة والأعضاء، مما يعزز من وظائف الجسم.\n\nإزالة السموم: تساعد في التخلص من السموم والشوائب المتراكمة في الجسم.",
//                   "price": 150,
//                   "createdAt": "2025-02-23T07:24:38.009Z",
//                   "updatedAt": "2025-05-11T13:01:52.231Z",
//                   "publishedAt": "2025-05-11T13:01:52.325Z",
//                   "locale": null,
//                   "stock": 200,
//                   "featured": true,
//                   "is_service": true,
//                   "sale_price": null,
//                   "session_time": 40,
//                   "sort": 1
//                 }
//               }
//             ],
//             "availableTimesByDay": {
//               "Tuesday": [
//                 {"time": "08:00", "datetime": "2025-06-24T05:00:00.000Z"},
//                 {"time": "08:15", "datetime": "2025-06-24T05:15:00.000Z"},
//                 {"time": "08:30", "datetime": "2025-06-24T05:30:00.000Z"},
//                 {"time": "08:45", "datetime": "2025-06-24T05:45:00.000Z"},
//                 {"time": "09:00", "datetime": "2025-06-24T06:00:00.000Z"},
//                 {"time": "09:15", "datetime": "2025-06-24T06:15:00.000Z"},
//                 {"time": "09:30", "datetime": "2025-06-24T06:30:00.000Z"},
//                 {"time": "09:45", "datetime": "2025-06-24T06:45:00.000Z"},
//                 {"time": "10:00", "datetime": "2025-06-24T07:00:00.000Z"},
//                 {"time": "10:15", "datetime": "2025-06-24T07:15:00.000Z"},
//                 {"time": "10:30", "datetime": "2025-06-24T07:30:00.000Z"},
//                 {"time": "10:45", "datetime": "2025-06-24T07:45:00.000Z"},
//                 {"time": "11:00", "datetime": "2025-06-24T08:00:00.000Z"},
//                 {"time": "11:15", "datetime": "2025-06-24T08:15:00.000Z"},
//                 {"time": "11:30", "datetime": "2025-06-24T08:30:00.000Z"},
//                 {"time": "11:45", "datetime": "2025-06-24T08:45:00.000Z"},
//                 {"time": "12:00", "datetime": "2025-06-24T09:00:00.000Z"},
//                 {"time": "12:15", "datetime": "2025-06-24T09:15:00.000Z"},
//                 {"time": "12:30", "datetime": "2025-06-24T09:30:00.000Z"},
//                 {"time": "12:45", "datetime": "2025-06-24T09:45:00.000Z"},
//                 {"time": "13:00", "datetime": "2025-06-24T10:00:00.000Z"},
//                 {"time": "13:15", "datetime": "2025-06-24T10:15:00.000Z"},
//                 {"time": "13:30", "datetime": "2025-06-24T10:30:00.000Z"},
//                 {"time": "13:45", "datetime": "2025-06-24T10:45:00.000Z"},
//                 {"time": "14:00", "datetime": "2025-06-24T11:00:00.000Z"},
//                 {"time": "14:15", "datetime": "2025-06-24T11:15:00.000Z"},
//                 {"time": "14:30", "datetime": "2025-06-24T11:30:00.000Z"},
//                 {"time": "14:45", "datetime": "2025-06-24T11:45:00.000Z"},
//                 {"time": "15:00", "datetime": "2025-06-24T12:00:00.000Z"},
//                 {"time": "15:15", "datetime": "2025-06-24T12:15:00.000Z"},
//                 {"time": "15:30", "datetime": "2025-06-24T12:30:00.000Z"},
//                 {"time": "15:45", "datetime": "2025-06-24T12:45:00.000Z"},
//                 {"time": "16:00", "datetime": "2025-06-24T13:00:00.000Z"}
//               ],
//               "Wednesday": [
//                 {"time": "12:40", "datetime": "2025-06-25T09:40:00.000Z"},
//                 {"time": "12:55", "datetime": "2025-06-25T09:55:00.000Z"},
//                 {"time": "13:10", "datetime": "2025-06-25T10:10:00.000Z"},
//                 {"time": "13:25", "datetime": "2025-06-25T10:25:00.000Z"},
//                 {"time": "13:40", "datetime": "2025-06-25T10:40:00.000Z"},
//                 {"time": "13:55", "datetime": "2025-06-25T10:55:00.000Z"},
//                 {"time": "14:10", "datetime": "2025-06-25T11:10:00.000Z"},
//                 {"time": "14:25", "datetime": "2025-06-25T11:25:00.000Z"},
//                 {"time": "14:40", "datetime": "2025-06-25T11:40:00.000Z"},
//                 {"time": "14:55", "datetime": "2025-06-25T11:55:00.000Z"},
//                 {"time": "15:10", "datetime": "2025-06-25T12:10:00.000Z"},
//                 {"time": "15:25", "datetime": "2025-06-25T12:25:00.000Z"}
//               ],
//               "Monday": [
//                 {"time": "08:00", "datetime": "2025-06-23T05:00:00.000Z"},
//                 {"time": "08:15", "datetime": "2025-06-23T05:15:00.000Z"},
//                 {"time": "08:30", "datetime": "2025-06-23T05:30:00.000Z"},
//                 {"time": "08:45", "datetime": "2025-06-23T05:45:00.000Z"},
//                 {"time": "09:00", "datetime": "2025-06-23T06:00:00.000Z"},
//                 {"time": "09:15", "datetime": "2025-06-23T06:15:00.000Z"},
//                 {"time": "09:30", "datetime": "2025-06-23T06:30:00.000Z"},
//                 {"time": "09:45", "datetime": "2025-06-23T06:45:00.000Z"},
//                 {"time": "10:00", "datetime": "2025-06-23T07:00:00.000Z"},
//                 {"time": "10:15", "datetime": "2025-06-23T07:15:00.000Z"},
//                 {"time": "10:30", "datetime": "2025-06-23T07:30:00.000Z"},
//                 {"time": "10:45", "datetime": "2025-06-23T07:45:00.000Z"},
//                 {"time": "11:00", "datetime": "2025-06-23T08:00:00.000Z"},
//                 {"time": "11:15", "datetime": "2025-06-23T08:15:00.000Z"},
//                 {"time": "11:30", "datetime": "2025-06-23T08:30:00.000Z"},
//                 {"time": "11:45", "datetime": "2025-06-23T08:45:00.000Z"},
//                 {"time": "12:00", "datetime": "2025-06-23T09:00:00.000Z"},
//                 {"time": "12:15", "datetime": "2025-06-23T09:15:00.000Z"},
//                 {"time": "12:30", "datetime": "2025-06-23T09:30:00.000Z"},
//                 {"time": "12:45", "datetime": "2025-06-23T09:45:00.000Z"},
//                 {"time": "13:00", "datetime": "2025-06-23T10:00:00.000Z"},
//                 {"time": "13:15", "datetime": "2025-06-23T10:15:00.000Z"},
//                 {"time": "13:30", "datetime": "2025-06-23T10:30:00.000Z"},
//                 {"time": "13:45", "datetime": "2025-06-23T10:45:00.000Z"},
//                 {"time": "14:00", "datetime": "2025-06-23T11:00:00.000Z"},
//                 {"time": "14:15", "datetime": "2025-06-23T11:15:00.000Z"},
//                 {"time": "14:30", "datetime": "2025-06-23T11:30:00.000Z"},
//                 {"time": "14:45", "datetime": "2025-06-23T11:45:00.000Z"},
//                 {"time": "15:00", "datetime": "2025-06-23T12:00:00.000Z"},
//                 {"time": "15:15", "datetime": "2025-06-23T12:15:00.000Z"},
//                 {"time": "15:30", "datetime": "2025-06-23T12:30:00.000Z"},
//                 {"time": "15:45", "datetime": "2025-06-23T12:45:00.000Z"},
//                 {"time": "16:00", "datetime": "2025-06-23T13:00:00.000Z"},
//                 {"time": "16:15", "datetime": "2025-06-23T13:15:00.000Z"},
//                 {"time": "16:30", "datetime": "2025-06-23T13:30:00.000Z"},
//                 {"time": "16:45", "datetime": "2025-06-23T13:45:00.000Z"},
//                 {"time": "17:00", "datetime": "2025-06-23T14:00:00.000Z"},
//                 {"time": "17:15", "datetime": "2025-06-23T14:15:00.000Z"},
//                 {"time": "17:30", "datetime": "2025-06-23T14:30:00.000Z"},
//                 {"time": "17:45", "datetime": "2025-06-23T14:45:00.000Z"},
//                 {"time": "18:00", "datetime": "2025-06-23T15:00:00.000Z"},
//                 {"time": "18:15", "datetime": "2025-06-23T15:15:00.000Z"},
//                 {"time": "18:30", "datetime": "2025-06-23T15:30:00.000Z"},
//                 {"time": "18:45", "datetime": "2025-06-23T15:45:00.000Z"},
//                 {"time": "19:00", "datetime": "2025-06-23T16:00:00.000Z"},
//                 {"time": "19:15", "datetime": "2025-06-23T16:15:00.000Z"},
//                 {"time": "19:30", "datetime": "2025-06-23T16:30:00.000Z"},
//                 {"time": "19:45", "datetime": "2025-06-23T16:45:00.000Z"},
//                 {"time": "20:00", "datetime": "2025-06-23T17:00:00.000Z"},
//                 {"time": "20:15", "datetime": "2025-06-23T17:15:00.000Z"},
//                 {"time": "20:30", "datetime": "2025-06-23T17:30:00.000Z"},
//                 {"time": "20:45", "datetime": "2025-06-23T17:45:00.000Z"},
//                 {"time": "21:00", "datetime": "2025-06-23T18:00:00.000Z"},
//                 {"time": "21:15", "datetime": "2025-06-23T18:15:00.000Z"},
//                 {"time": "21:30", "datetime": "2025-06-23T18:30:00.000Z"},
//                 {"time": "21:45", "datetime": "2025-06-23T18:45:00.000Z"},
//                 {"time": "22:00", "datetime": "2025-06-23T19:00:00.000Z"},
//                 {"time": "22:15", "datetime": "2025-06-23T19:15:00.000Z"},
//                 {"time": "22:30", "datetime": "2025-06-23T19:30:00.000Z"},
//                 {"time": "22:45", "datetime": "2025-06-23T19:45:00.000Z"},
//                 {"time": "23:00", "datetime": "2025-06-23T20:00:00.000Z"},
//                 {"time": "23:15", "datetime": "2025-06-23T20:15:00.000Z"},
//                 {"time": "23:30", "datetime": "2025-06-23T20:30:00.000Z"}
//               ]
//             }
//           }
//         ];
