import 'package:barber_app/src/screens/auth/models/city_model.dart';
import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/network/api_endpoints.dart';

enum UserGender { male, female }

class UserModel extends Equatable {
  final int? id;
  final String? documentId;
  final String name;
  final String address;
  final LatLng? latLng;
  final UserGender? gender;
  final CityModel? city;
  final String phone;
  final String password;
  final String deviceToken;

  const UserModel({
    this.id,
    this.documentId = '',
    this.name = '',
    this.phone = '',
    this.password = '',
    this.deviceToken = '',
    this.address = '',
    this.gender = UserGender.male,
    this.city,
    this.latLng,
  });

  String get email => '$<EMAIL>';

  // * For Login ================================
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      documentId: json['documentId'] ?? '',
      name: json['username'] ?? '',
      phone: json['phone'] ?? '',
      deviceToken: json['fcm_token'] ?? '',
      password: json['password'] ?? '',
      address: json['address'] ?? '',
      gender: json['gender'] == 'female' ? UserGender.female : UserGender.male,
      city: json['city'] != null ? CityModel.fromJson(json['city']) : null,
      latLng: json['lat'] != null && json['lng'] != null
          ? LatLng(
              double.tryParse(json['lat'].toString()) ?? 0,
              double.tryParse(json['lng'].toString()) ?? 0,
            )
          : null,
    );
  }

  //? Copy With
  UserModel copyWith({
    int? id,
    String? documentId,
    String? name,
    String? phone,
    String? password,
    String? deviceToken,
    String? address,
    UserGender? gender,
    CityModel? city,
    LatLng? latLng,
  }) {
    return UserModel(
      id: id ?? this.id,
      documentId: name ?? this.documentId,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      password: password ?? this.password,
      deviceToken: deviceToken ?? this.deviceToken,
      address: address ?? this.address,
      gender: gender ?? this.gender,
      city: city ?? this.city,
      latLng: latLng ?? this.latLng,
    );
  }

  // * To Json ================================
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (documentId?.isNotEmpty ?? false) 'documentId': documentId,
      if (name.isNotEmpty) 'username': name,
      if (phone.isNotEmpty) ...{
        'phone': phone,
        'email': '$<EMAIL>',
      },
      if (deviceToken.isNotEmpty) 'fcm_token': deviceToken,
      if (password.isNotEmpty) 'password': password,
      if (address.isNotEmpty) 'address': address,
      if (gender != null) 'gender': gender!.name,
      if (city != null) 'city': city?.toJson(),
      if (latLng != null) ...{
        'lat': latLng!.latitude.toString(),
        'lng': latLng!.longitude.toString(),
      },
    };
  }

  // to update json
  Map<String, dynamic> toUpdateJson() {
    return {
      if (name.isNotEmpty) 'username': name,
      // if (phone.isNotEmpty) 'phone': phone,
      if (address.isNotEmpty) 'address': address,
      if (gender != null) 'gender': gender!.name,
      if (city != null) 'city': city?.id
    };
  }

  // * To Json ================================
  Map<String, dynamic> toLoginJson() {
    return {
      'identifier': '${filteredPhone(phone)}@barber.com',
      'password': password,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        password,
        deviceToken,
        address,
        gender,
        city,
      ];

  //? Get saved user
  static UserModel get currentUser {
    final userData = GetStorageService.getData(key: LocalKeys.user);

    if (userData == null) {
      return UserModel.empty();
    } else {
      return UserModel.fromJson(userData);
    }
  }

  // * City Fee
  static CityModel get userCity {
    final currentUserCity = currentUser.city;

    return currentUserCity ?? CityModel.empty();
  }

  factory UserModel.empty() {
    return const UserModel();
  }

  static bool isLogged() {
    final userData = GetStorageService.getData(key: LocalKeys.user);

    return userData != null;
  }
}
