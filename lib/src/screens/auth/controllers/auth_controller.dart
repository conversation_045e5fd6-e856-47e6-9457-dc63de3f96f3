import 'package:barber_app/src/core/consts/network/api_strings.dart';
import 'package:barber_app/src/screens/auth/models/city_model.dart';
import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/auth/repositories/auth_repository.dart';
import 'package:barber_app/src/screens/auth/view/login/login.screen.dart';
import 'package:barber_app/src/screens/main_screen/view/main.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthController extends BaseController {
  final AuthRepository authRepo;

  AuthController({
    required this.authRepo,
  });

  // * Login
  Future<bool> login({
    required Map<String, dynamic> data,
  }) async {
    return await baseControllerFunction(
      () async {
        final user = await _setUser(data);

        final userData = await authRepo.login(user: user);

        return userData;
      },
      additionalFunction: () {
        const MainScreen().navigate;
      },
    );
  }

  // * Register
  Future<bool> register({
    required Map<String, dynamic> data,
    required UserGender userGender,
    required CityModel city,
  }) async {
    return await baseControllerFunction(
      () async {
        final user = await _setUser(
          data,
          gender: userGender,
          city: city,
        );

        final logged = await authRepo.register(user: user);

        return logged;
      },
      additionalFunction: () {
        const MainScreen().navigate;
      },
    );
  }

  // * Set User
  Future<UserModel> _setUser(
    Map<String, dynamic> data, {
    UserGender? gender,
    CityModel? city,
  }) async {
    final fcmToken = await NotificationService.getToken();

    final user = UserModel(
      name: data[FieldsConsts.name] ?? '',
      phone: data[FieldsConsts.phone] ?? '',
      password: data[FieldsConsts.password] ?? '',
      address: data[FieldsConsts.address] ?? '',
      gender: gender,
      city: city,
      deviceToken: fcmToken,
    );

    return user;
  }

  // * Logout
  Future<void> logout() async {
    return await baseControllerFunction(
      () async {
        await authRepo.logout();

        const LoginScreen().navigateReplacement;
      },
    );
  }

  Future<bool> updateProfile(
      {required Map<String, dynamic> data, CityModel? city}) async {
    return await baseControllerFunction(
      () async {
        final updatedUser = UserModel(
          id: UserModel.currentUser.id,
          name: data['name'] ?? UserModel.currentUser.name,
          phone: data['phone'] ?? UserModel.currentUser.phone,
          address: data['address'] ?? UserModel.currentUser.address,
          city: city,
        );

        return await authRepo.updateProfile(user: updatedUser);
      },
    );
  }

  // * Get Cities
  Future<List<CityModel>> getCities() async {
    return await baseControllerFunction(
      () async {
        return await authRepo.getCities();
      },
    );
  }

  // * Get Providers
  Future<List<ProviderModel>> getProviders() async {
    return await baseControllerFunction(
      () async {
        return await authRepo.getProviders();
      },
    );
  }

  // * Get Providers By City & Service
  Future<List<ProviderModel>> getProvidersByCityAndService({
    required String? serviceDocId,
  }) async {
    return await baseControllerFunction(
      () async {
        return await authRepo.getProvidersByCityAndService(
          serviceDocId: serviceDocId,
        );
      },
    );
  }

  // * Get Closest Provider By Lat Lng
  Future<ProviderModel?> getClosestAvailableProviderByLatLng({
    required String? lat,
    required String? lng,
    DateTime? date,
    required String serviceDocId,
  }) async {
    return await baseControllerFunction(
      () async {
        return await authRepo.getClosestProviderByLatLng(
          lat: lat,
          lng: lng,
          date: date,
          serviceDocId: serviceDocId,
        );
      },
    );
  }

  // * Get Provider Available Times By Lat Lng
  Future<ProviderModel?> getProviderAvailableTimesByLatLng({
    required String? lat,
    required String? lng,
    required String serviceDocId,
    required String providerDocId,
  }) async {
    return await baseControllerFunction(
      () async {
        return await authRepo.getProviderAvailableTimesByLatLng(
          lat: lat,
          lng: lng,
          serviceDocId: serviceDocId,
          providerDocId: providerDocId,
        );
      },
    );
  }

  // * Reset Password
  Future<bool> resetPassword({
    required String phone,
    required String newPassword,
  }) async {
    return await baseControllerFunction(
      () async {
        return await authRepo.resetPassword(
          phone: phone,
          newPassword: newPassword,
        );
      },
    );
  }
}
