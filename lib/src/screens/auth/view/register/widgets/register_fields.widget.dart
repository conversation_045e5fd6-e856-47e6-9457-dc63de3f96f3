import 'package:barber_app/src/core/consts/network/api_strings.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/drop_downs/city_drop_down.dart';
import 'package:barber_app/src/core/shared/widgets/fields/text_field.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/controllers/verify_otp.controller.dart';
import 'package:barber_app/src/screens/auth/models/city_model.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/auth/view/login/login.screen.dart';
import 'package:barber_app/src/screens/auth/view/register/widgets/choose_gender.widget.dart';
import 'package:barber_app/src/screens/main_screen/view/main.screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/widgets/verify_bottom_sheet/verify_code_bottom_sheet.dart';
import '../../../providers/auth_providers.dart';

class RegisterFieldsWidget extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const RegisterFieldsWidget({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final verifyOTPController = ref.watch(verifyOTPControllerProvider);

    final phoneController = useTextEditingController();
    final passwordController = useTextEditingController();

    final valueNotifiers = {
      FieldsConsts.city: useState<CityModel?>(null),
      FieldsConsts.gender: useState<UserGender>(UserGender.male),
      FieldsConsts.isPhoneVerified: useState(false),
    };

    final city =
        valueNotifiers[FieldsConsts.city]! as ValueNotifier<CityModel?>;
    final gender =
        valueNotifiers[FieldsConsts.gender]! as ValueNotifier<UserGender>;
    final isPhoneVerified =
        valueNotifiers[FieldsConsts.isPhoneVerified]! as ValueNotifier<bool>;

    void register() async {
      if (!formKey.currentState!.saveAndValidate()) return;

      final data = formKey.currentState?.instantValue ?? {};

      if (city.value == null) {
        showToast(context.tr.pleaseChooseYourCity, isError: true);
        return;
      }

      try {
        await authController.register(
          data: data,
          userGender: gender.value,
          city: city.value!,
        );
      } catch (e) {
        // Handle Strapi errors
        if (e.toString().contains('phone') ||
            e.toString().contains('mobile') ||
            e.toString().contains('already') ||
            e.toString().contains('taken') ||
            e.toString().contains('exists')) {
          showToast(context.tr.mobileNumberAlreadyTaken, isError: true);
        } else {
          showToast(e.toString(), isError: true);
        }
      }
    }

    void showVerifyBottomSheet() async {
      if (!formKey.currentState!.saveAndValidate()) return;

      try {
        await verifyOTPController.signInWithPhoneNumber(
            phone: phoneController.text);

        showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppRadius.radius12),
                    topRight: Radius.circular(AppRadius.radius12))),
            builder: (ctx) => VerifyOtpBottomSheet(
                  onSuccess: (otp) {
                    register();
                  },
                  phoneNumber: phoneController.text,
                  shouldValidatePhone: true,
                  phoneVerified: isPhoneVerified,
                ));
      } catch (e) {
        if (e.toString().contains('Daily OTP limit reached')) {
          showToast(context.tr.otpLimitReached, isError: true);
        } else {
          showToast(e.toString(), isError: true);
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        //! Full Name ------------------------------
        BaseTextField(
          name: FieldsConsts.name,
          title: context.tr.fullName,
        ),

        AppGaps.gap12,

        //! Address ------------------------------
        BaseTextField(
          name: FieldsConsts.address,
          title: context.tr.address,
        ),

        AppGaps.gap24,

        //! City
        CityDropDown(
          selectedCity: city,
        ),

        AppGaps.gap12,

        //! Mobile Number ------------------------------
        BaseTextField(
          enabled: !isPhoneVerified.value,
          name: FieldsConsts.phone,
          controller: phoneController,
          icon: isPhoneVerified.value
              ? const Icon(
                  Icons.check_circle,
                  color: ColorManager.successColor,
                )
              : null,
          hint: '0534576556',
          title: context.tr.mobileNumber,
          textAlign: TextAlign.left,
          withoutEnter: true,
          // suffixIcon: Text(
          //   '+972',
          //   style: AppTextStyles.labelLarge,
          //   textDirection: TextDirection.ltr,
          // ).paddingOnly(top: AppSpaces.padding8 - 2, left: AppSpaces.padding8),
        ),

        AppGaps.gap12,

        //! Password ------------------------------
        BaseTextField(
          name: FieldsConsts.password,
          title: context.tr.password,
          controller: passwordController,
          hint: '********',
          textInputType: TextInputType.visiblePassword,
          withoutEnter: true,
          isObscure: true,
        ),

        AppGaps.gap12,

        //! Confirm Password ------------------------------
        BaseTextField(
          name: FieldsConsts.confirmPassword,
          title: context.tr.confirmPassword,
          hint: '********',
          textInputType: TextInputType.visiblePassword,
          withoutEnter: true,
          isObscure: true,
        ),

        AppGaps.gap12,

        ChooseGenderWidget(
          gender: gender,
        ),

        AppGaps.gap12,

        Button(
          isLoading: authController.isLoading,
          label: context.tr.register,
          textColor: Colors.black,
          isWhiteText: false,
          onPressed: showVerifyBottomSheet,
          // register,
        ),

        AppGaps.gap16,

        Text(context.tr.haveAnAccount, style: AppTextStyles.subTitle),

        AppGaps.gap16,

        Button(
          isOutLine: true,
          color: ColorManager.secondaryColor,
          label: context.tr.login,
          onPressed: () {
            const LoginScreen().navigate;
          },
        ),

        AppGaps.gap8,

        TextButton(
          onPressed: () {
            const MainScreen().navigate;
          },
          child: Text(context.tr.skip,
              style: AppTextStyles.labelLarge.copyWith(
                color: ColorManager.blue,
              )),
        )
      ],
    );
  }
}
