import 'package:barber_app/generated/assets.gen.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/screens/auth/view/register/widgets/register_fields.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class RegisterScreen extends HookConsumerWidget {
  const RegisterScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Scaffold(
      body: FormBuilder(
        key: formKey,
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding12),
          child: <PERSON><PERSON>ie<PERSON>(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(AppRadius.radius28),
                child:
                    Assets.images.logo.image(fit: BoxFit.cover, width: 220.w),
              ).center(),

              AppGaps.gap12,

              Text(
                context.tr.register,
                style: AppTextStyles.subHeadLine.copyWith(
                  fontWeight: FontWeight.normal,
                ),
              ).center(),

              AppGaps.gap24,

              // * Fields Container
              RegisterFieldsWidget(
                formKey: formKey,
              )
            ],
          ),
        ),
      ),
    );
  }
}
