import 'package:barber_app/src/core/consts/network/api_endpoints.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/verify_bottom_sheet/verify_code_bottom_sheet.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/consts/network/api_strings.dart';
import '../../../../../core/shared/widgets/drop_downs/city_drop_down.dart';
import '../../../../../core/shared/widgets/fields/text_field.dart';
import '../../../../main_screen/view/main.screen.dart';
import '../../../controllers/verify_otp.controller.dart';
import '../../../models/city_model.dart';
import '../../../models/user_model.dart';
import '../../../providers/auth_providers.dart';
import '../../register/widgets/choose_gender.widget.dart';

class UpdateProfileFieldsWidget extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const UpdateProfileFieldsWidget({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider);

    final phoneController = useTextEditingController(
        text: filteredPhone(UserModel.currentUser.phone,
            removeCountryCode: true));

    final valueNotifiers = {
      FieldsConsts.city: useState<CityModel?>(null),
      FieldsConsts.gender:
          useState<UserGender>(UserModel.currentUser.gender ?? UserGender.male),
    };

    final city =
        valueNotifiers[FieldsConsts.city]! as ValueNotifier<CityModel?>;
    final gender =
        valueNotifiers[FieldsConsts.gender]! as ValueNotifier<UserGender>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        //! Full Name ------------------------------
        BaseTextField(
          name: FieldsConsts.name,
          title: context.tr.fullName,
          initialValue: UserModel.currentUser.name,
        ),

        AppGaps.gap12,

        //! Address ------------------------------
        BaseTextField(
          name: FieldsConsts.address,
          title: context.tr.address,
          initialValue: UserModel.currentUser.address,
        ),

        AppGaps.gap24,

        //! City
        CityDropDown(
          selectedCity: city,
          selectedCityName: UserModel.currentUser.city?.name,
        ),

        AppGaps.gap12,

        //! Mobile Number ------------------------------
        BaseTextField(
          enabled: false,
          // !isPhoneVerified.value,
          name: FieldsConsts.phone,
          controller: phoneController,
          // icon: isPhoneVerified.value
          //     ? const Icon(
          //   Icons.check_circle,
          //   color: ColorManager.successColor,
          // )
          //     : null,
          hint: '0534576556',
          title: context.tr.mobileNumber,
          textAlign: TextAlign.left,
          withoutEnter: true,
        ),

        AppGaps.gap12,

        //! Password ------------------------------
        // BaseTextField(
        //   name: FieldsConsts.password,
        //   title: context.tr.password,
        //   controller: passwordController,
        //   hint: '********',
        //   textInputType: TextInputType.visiblePassword,
        //   isObscure: true,
        // ),

        AppGaps.gap12,

        //! Gender ------------------------------
        ChooseGenderWidget(
          gender: gender,
        ),

        AppGaps.gap24,

        Button(
          isWhiteText: false,
          onPressed: () async {
            if (formKey.currentState?.saveAndValidate() ?? false) {
              final formData = formKey.currentState?.value;
              await authController.updateProfile(
                data: formData!,
                city: city.value,
              );

              const MainScreen().navigateReplacement;

              showToast(
                context.tr.profileUpdatedSuccessfully,
              );
            }
          },
          label: context.tr.save,
        ),
      ],
    );
  }
}
