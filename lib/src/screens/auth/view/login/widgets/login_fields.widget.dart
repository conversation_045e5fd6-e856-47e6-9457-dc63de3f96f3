import 'package:barber_app/src/core/consts/network/api_strings.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/fields/text_field.dart';
import 'package:barber_app/src/core/shared/widgets/loading/loading.widget.dart';
import 'package:barber_app/src/core/shared/widgets/verify_bottom_sheet/verify_code_bottom_sheet.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/controllers/verify_otp.controller.dart';
import 'package:barber_app/src/screens/auth/providers/auth_providers.dart';
import 'package:barber_app/src/screens/auth/view/register/register.screen.dart';
import 'package:barber_app/src/screens/main_screen/view/main.screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginFieldsWidget extends ConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const LoginFieldsWidget({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);

    void login() {
      if (!formKey.currentState!.saveAndValidate()) return;

      final data = formKey.currentState?.instantValue ?? {};

      authController.login(
        data: data,
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppGaps.gap24,

        //! Mobile Number ------------------------------
        BaseTextField(
          name: FieldsConsts.phone,
          hint: '534576556',
          initialValue: '**********',
          title: context.tr.mobileNumber,
          textAlign: TextAlign.left,
          withoutEnter: true,
          // suffixIcon: Text(
          //   '+972',
          //   style: AppTextStyles.labelLarge,
          //   textDirection: TextDirection.ltr,
          // ).paddingOnly(top: AppSpaces.padding8 - 2, left: AppSpaces.padding8),
        ),

        AppGaps.gap12,

        //! Password ------------------------------
        BaseTextField(
          name: FieldsConsts.password,
          title: context.tr.password,
          initialValue: '123456789',
          hint: '********',
          textInputType: TextInputType.visiblePassword,
          isObscure: true,
          withoutEnter: true,
        ),

        AppGaps.gap8,

        TextButton(
          onPressed: () {},
          child: Text(context.tr.forgotPassword,
              style: AppTextStyles.labelLarge.copyWith(
                color: ColorManager.blue,
              )),
        ),

        AppGaps.gap24,

        Button(
          isLoading: authController.isLoading,
          label: context.tr.login,
          onPressed: login,
          loadingWidget: const LoadingWidget(),
          textColor: Colors.black,
          isWhiteText: false,
        ),

        AppGaps.gap16,

        Text(context.tr.dontHaveAnAccount, style: AppTextStyles.subTitle),

        AppGaps.gap16,

        Button(
          isOutLine: true,
          color: ColorManager.secondaryColor,
          label: context.tr.register,
          onPressed: () {
            const RegisterScreen().navigate;
          },
        ),

        AppGaps.gap8,

        TextButton(
          onPressed: () {
            const MainScreen().navigate;
          },
          child: Text(context.tr.skip,
              style: AppTextStyles.labelLarge.copyWith(
                color: ColorManager.blue,
              )),
        )
      ],
    );
  }
}
